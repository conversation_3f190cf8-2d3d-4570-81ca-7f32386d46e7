<template>
  <div class="video-config-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">视频采集配置</h2>
        <div class="header-actions">
          <el-button @click="handleSave">保存</el-button>
          <el-button type="primary" @click="handleSaveAsDefault">保存为默认配置</el-button>
          <el-button type="success" @click="handleSaveAndApply">保存并应用配置</el-button>
        </div>
      </div>
      <div class="header-description">
        <p>配置视频采集的各项参数，包括检测阈值、转码参数等。配置完成后可保存为默认配置或直接应用到采集任务中。</p>
      </div>
    </div>

    <!-- 配置内容区域 -->
    <div class="config-content">
      <!-- 静态检测参数与处理 -->
      <StaticDetectionConfig
        ref="staticDetectionRef"
        v-model="configData.staticDetection"
        @change="handleConfigChange"
      />

      <!-- 花屏/黑屏检测与处理 -->
      <ScreenDetectionConfig
        ref="screenDetectionRef"
        v-model="configData.screenDetection"
        @change="handleConfigChange"
      />

      <!-- 自定义规则设置 -->
      <CustomRulesConfig
        ref="customRulesRef"
        v-model="configData.customRules"
        @change="handleConfigChange"
      />
    </div>
  </div>
</template>

<script>
import StaticDetectionConfig from './components/StaticDetectionConfig.vue'
import ScreenDetectionConfig from './components/ScreenDetectionConfig.vue'
import CustomRulesConfig from './components/CustomRulesConfig.vue'
import request from '@/utils/request.js'

export default {
  name: 'VideoCollectionConfig',
  components: {
    StaticDetectionConfig,
    ScreenDetectionConfig,
    CustomRulesConfig
  },
  data() {
    return {
      loading: false,
      configData: {
        // 静态检测参数
        staticDetection: {
          detectionThreshold: 50, // 检测阈值 0-100%
          transcodeThreshold: 95, // 转码阈值 0-100%
          enableTranscode: true, // 启用转码处理
          transcodeParams: {
            maxSize: 3, // 最大文件大小 GB
            enableAutoTranscode: true, // 启用自动转码
            transcodeFormat: 'H264', // 转码格式
            description: '自动转码处理，可设置最大文件大小限制，超出限制时自动进行转码处理'
          }
        },
        // 花屏/黑屏检测参数
        screenDetection: {
          flowerScreen: {
            threshold: 5, // 花屏检测阈值 1-100
            percentage: 30, // 花屏检测百分比 10%-50%
            description: '检测视频中的花屏现象，当花屏像素超过设定阈值时触发处理机制，建议值：20-40%'
          },
          blackScreen: {
            threshold: 20, // 黑屏检测阈值 1-100
            percentage: 80, // 黑屏检测百分比 10%-50%
            description: '检测视频中的黑屏现象，当黑屏像素超过设定阈值时触发处理机制'
          },
          enableProcessing: true, // 启用检测处理
          processingMethod: 'auto_transcode', // 处理方式：自动转码、标记异常、不处理
          processingDescription: '自动检测并处理花屏/黑屏问题，可选择自动转码或仅标记异常'
        },
        // 自定义规则
        customRules: {
          enableCustomRules: false, // 启用自定义规则
          uploadedRules: [], // 已上传的规则文件
          ruleDescription: '上传自定义的视频处理规则文件，支持Python文件格式，文件大小不超过1MB',
          processingCode: '', // 自定义处理代码
          codeDescription: '自定义视频处理逻辑，使用Python语法编写处理函数'
        }
      }
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    // 加载配置数据
    async loadConfig() {
      try {
        this.loading = true
        const response = await request({
          url: '/system/AutoOsmotic/config',
          method: 'get',
          params: {
            type: 'video_collection_config'
          }
        })
        if (response.data) {
          this.configData = { ...this.configData, ...response.data }
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$message.error('加载配置失败')
      } finally {
        this.loading = false
      }
    },

    // 配置变更处理
    handleConfigChange() {
      // 配置发生变更时的处理逻辑
      console.log('配置已变更:', this.configData)
    },

    // 保存配置
    async handleSave() {
      try {
        await this.validateAllConfigs()

        await request({
          url: '/system/AutoOsmotic/config',
          method: 'post',
          data: {
            type: 'video_collection_config',
            config: this.configData
          }
        })

        this.$message.success('配置保存成功')
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败')
      }
    },

    // 保存为默认配置
    async handleSaveAsDefault() {
      try {
        await this.validateAllConfigs()

        await request({
          url: '/system/AutoOsmotic/config/default',
          method: 'post',
          data: {
            type: 'video_collection_config',
            config: this.configData
          }
        })

        this.$message.success('已保存为默认配置')
      } catch (error) {
        console.error('保存默认配置失败:', error)
        this.$message.error('保存默认配置失败')
      }
    },

    // 保存并应用配置
    async handleSaveAndApply() {
      try {
        await this.validateAllConfigs()

        await request({
          url: '/system/AutoOsmotic/config/apply',
          method: 'post',
          data: {
            type: 'video_collection_config',
            config: this.configData
          }
        })

        this.$message.success('配置已保存并应用')
      } catch (error) {
        console.error('保存并应用配置失败:', error)
        this.$message.error('保存并应用配置失败')
      }
    },

    // 验证所有配置
    async validateAllConfigs() {
      const refs = [
        this.$refs.staticDetectionRef,
        this.$refs.screenDetectionRef,
        this.$refs.customRulesRef
      ]

      for (const ref of refs) {
        if (ref && ref.validate) {
          const isValid = await ref.validate()
          if (!isValid) {
            throw new Error('配置验证失败')
          }
        }
      }
    }
  }
}
</script>

<style scoped>
.video-config-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
