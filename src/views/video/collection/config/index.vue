<template>
  <div class="video-config-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2 class="page-title">
            <i class="el-icon-video-camera"></i>
            智能视频异常检测配置
          </h2>
          <p class="page-subtitle">配置视频异常检测参数，包括静态帧、花屏、黑屏检测与处理策略</p>
        </div>
        <div class="header-actions">
          <el-button @click="handleSave" :loading="loading">
            <i class="el-icon-document"></i>
            保存配置
          </el-button>
          <el-button type="primary" @click="handleSaveAsDefault" :loading="loading">
            <i class="el-icon-star-on"></i>
            保存为默认
          </el-button>
          <el-button type="success" @click="handleSaveAndApply" :loading="loading">
            <i class="el-icon-check"></i>
            保存并应用
          </el-button>
        </div>
      </div>
    </div>

    <!-- 配置内容区域 -->
    <div class="config-content" v-loading="loading">
      <!-- 静态帧检测与处理 -->
      <StaticFrameDetectionConfig
        ref="staticFrameDetectionRef"
        v-model="configData.staticFrameDetection"
        @change="handleConfigChange"
      />

      <!-- 花屏/黑屏检测与处理 -->
      <ScreenAnomalyDetectionConfig
        ref="screenAnomalyDetectionRef"
        v-model="configData.screenAnomalyDetection"
        @change="handleConfigChange"
      />

      <!-- 自定义规则处理 -->
      <CustomRulesProcessingConfig
        ref="customRulesProcessingRef"
        v-model="configData.customRulesProcessing"
        @change="handleConfigChange"
      />
    </div>
  </div>
</template>

<script>
import StaticFrameDetectionConfig from './components/StaticFrameDetectionConfig.vue'
import ScreenAnomalyDetectionConfig from './components/ScreenAnomalyDetectionConfig.vue'
import CustomRulesProcessingConfig from './components/CustomRulesProcessingConfig.vue'
import request from '@/utils/request'

export default {
  name: 'VideoCollectionConfig',
  components: {
    StaticFrameDetectionConfig,
    ScreenAnomalyDetectionConfig,
    CustomRulesProcessingConfig
  },
  data() {
    return {
      loading: false,
      configData: {
        // 静态帧检测与处理
        staticFrameDetection: {
          // 连续帧数阈值：用于判断画面是否为静止帧
          consecutiveFrameThreshold: 5, // 连续帧数 1-30
          // 特征相似度阈值：两个画面帧之间的相似度超过该值则判断为静止帧
          similarityThreshold: 95, // 相似度百分比 80-99%
          // 处理方式
          processingMethod: 'auto_delete', // auto_delete: 自动删除, mark_only: 仅标记不删除, compress: 压缩处理(保留首帧)
          // 启用静态帧检测
          enableDetection: true,
          // 描述信息
          description: '检测视频中的静态帧（如卡顿、静止画面），当连续帧数和相似度超过设定阈值时触发处理机制'
        },
        // 花屏/黑屏检测与处理
        screenAnomalyDetection: {
          // 黑屏检测配置
          blackScreen: {
            // 黑屏判定阈值：通过图像亮度值判断是否为黑屏
            brightnessThreshold: 15, // 亮度阈值 0-100
            // 黑屏像素比例阈值
            pixelRatioThreshold: 85, // 像素比例 50-100%
            description: '检测视频中的黑屏现象，当图像亮度低于阈值且黑屏像素比例超过设定值时触发处理'
          },
          // 花屏检测配置
          flowerScreen: {
            // 花屏判定阈值：通过图像异常比例判定
            anomalyRatioThreshold: 25, // 异常比例 10-50%
            // 噪点密度阈值
            noiseDensityThreshold: 30, // 噪点密度 10-60%
            description: '检测视频中的花屏现象，当图像异常比例和噪点密度超过设定阈值时触发处理'
          },
          // 最小异常持续时间：定义异常需持续多久才触发处理
          minAnomalyDuration: 2, // 持续时间（秒） 0.5-10
          // 处理方式
          processingMethod: 'auto_delete', // auto_delete: 自动删除异常帧, replace_normal: 使用正常帧替换, log_only: 仅记录并报警不修改视频
          // 启用异常检测
          enableDetection: true,
          // 描述信息
          description: '检测并处理视频中的花屏、黑屏等异常现象，支持多种处理策略'
        },
        // 自定义规则处理
        customRulesProcessing: {
          // 启用自定义规则
          enableCustomRules: false,
          // 已上传的Python脚本文件
          uploadedScripts: [],
          // 脚本功能说明
          scriptDescription: '',
          // 脚本启用状态
          scriptEnabled: false,
          // 接口说明和模板
          interfaceDescription: '自定义脚本需要实现标准的处理函数接口，支持帧级别的检测和处理逻辑',
          // 处理函数模板
          functionTemplate: `def process_frame(frame_data, frame_index, metadata):
    """
    自定义帧处理函数

    参数:
        frame_data: 帧图像数据 (numpy array)
        frame_index: 帧索引 (int)
        metadata: 帧元数据 (dict)

    返回:
        dict: {
            'action': 'keep|delete|replace',  # 处理动作
            'confidence': 0.0-1.0,           # 置信度
            'reason': 'string'               # 处理原因
        }
    """
    # 在此处编写自定义检测逻辑
    return {
        'action': 'keep',
        'confidence': 1.0,
        'reason': 'normal_frame'
    }`,
          // 描述信息
          description: '上传自定义Python脚本扩展检测逻辑，支持高级自定义处理策略'
        }
      }
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    // 加载配置数据
    async loadConfig() {
      try {
        this.loading = true
        const response = await request({
          url: '/system/AutoOsmotic/config',
          method: 'get',
          params: {
            type: 'video_collection_config'
          }
        })
        if (response.data) {
          this.configData = { ...this.configData, ...response.data }
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$message.error('加载配置失败')
      } finally {
        this.loading = false
      }
    },

    // 配置变更处理
    handleConfigChange() {
      // 配置发生变更时的处理逻辑
      console.log('配置已变更:', this.configData)
    },

    // 保存配置
    async handleSave() {
      try {
        await this.validateAllConfigs()

        await request({
          url: '/system/AutoOsmotic/config',
          method: 'post',
          data: {
            type: 'video_collection_config',
            config: this.configData
          }
        })

        this.$message.success('配置保存成功')
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败')
      }
    },

    // 保存为默认配置
    async handleSaveAsDefault() {
      try {
        await this.validateAllConfigs()

        await request({
          url: '/system/AutoOsmotic/config/default',
          method: 'post',
          data: {
            type: 'video_collection_config',
            config: this.configData
          }
        })

        this.$message.success('已保存为默认配置')
      } catch (error) {
        console.error('保存默认配置失败:', error)
        this.$message.error('保存默认配置失败')
      }
    },

    // 保存并应用配置
    async handleSaveAndApply() {
      try {
        await this.validateAllConfigs()

        await request({
          url: '/system/AutoOsmotic/config/apply',
          method: 'post',
          data: {
            type: 'video_collection_config',
            config: this.configData
          }
        })

        this.$message.success('配置已保存并应用')
      } catch (error) {
        console.error('保存并应用配置失败:', error)
        this.$message.error('保存并应用配置失败')
      }
    },

    // 验证所有配置
    async validateAllConfigs() {
      const refs = [
        this.$refs.staticFrameDetectionRef,
        this.$refs.screenAnomalyDetectionRef,
        this.$refs.customRulesProcessingRef
      ]

      for (const ref of refs) {
        if (ref && ref.validate) {
          const isValid = await ref.validate()
          if (!isValid) {
            throw new Error('配置验证失败')
          }
        }
      }
    }
  }
}
</script>

<style scoped>
.video-config-page {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title i {
  font-size: 32px;
  color: #ffd700;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.header-actions .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.header-actions .el-button--primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-color: transparent;
}

.header-actions .el-button--success {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  border-color: transparent;
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-config-page {
    padding: 16px;
  }

  .page-header {
    padding: 24px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .header-actions {
    justify-content: center;
  }

  .page-title {
    font-size: 24px;
    text-align: center;
  }

  .page-subtitle {
    text-align: center;
  }
}

/* 加载动画优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

:deep(.el-loading-spinner) {
  color: #667eea;
}
</style>
