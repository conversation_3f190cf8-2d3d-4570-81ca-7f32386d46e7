<template>
  <div class="test-page">
    <h1>视频采集配置页面测试</h1>
    
    <!-- 静态检测参数组件测试 -->
    <div class="test-section">
      <h2>静态检测参数组件测试</h2>
      <StaticDetectionConfig 
        v-model="staticDetectionData"
        @change="handleStaticDetectionChange"
      />
    </div>

    <!-- 花屏/黑屏检测组件测试 -->
    <div class="test-section">
      <h2>花屏/黑屏检测组件测试</h2>
      <ScreenDetectionConfig 
        v-model="screenDetectionData"
        @change="handleScreenDetectionChange"
      />
    </div>

    <!-- 自定义规则组件测试 -->
    <div class="test-section">
      <h2>自定义规则组件测试</h2>
      <CustomRulesConfig 
        v-model="customRulesData"
        @change="handleCustomRulesChange"
      />
    </div>

    <!-- 数据展示 -->
    <div class="test-section">
      <h2>当前配置数据</h2>
      <el-card>
        <h3>静态检测参数</h3>
        <pre>{{ JSON.stringify(staticDetectionData, null, 2) }}</pre>
        
        <h3>花屏/黑屏检测参数</h3>
        <pre>{{ JSON.stringify(screenDetectionData, null, 2) }}</pre>
        
        <h3>自定义规则参数</h3>
        <pre>{{ JSON.stringify(customRulesData, null, 2) }}</pre>
      </el-card>
    </div>
  </div>
</template>

<script>
import StaticDetectionConfig from './components/StaticDetectionConfig.vue'
import ScreenDetectionConfig from './components/ScreenDetectionConfig.vue'
import CustomRulesConfig from './components/CustomRulesConfig.vue'

export default {
  name: 'VideoConfigTest',
  components: {
    StaticDetectionConfig,
    ScreenDetectionConfig,
    CustomRulesConfig
  },
  data() {
    return {
      staticDetectionData: {
        detectionThreshold: 50,
        transcodeThreshold: 95,
        enableTranscode: true,
        transcodeParams: {
          maxSize: 3,
          enableAutoTranscode: true,
          transcodeFormat: 'H264',
          description: '自动转码处理，可设置最大文件大小限制，超出限制时自动进行转码处理'
        }
      },
      screenDetectionData: {
        flowerScreen: {
          threshold: 5,
          percentage: 30,
          description: '检测视频中的花屏现象，当花屏像素超过设定阈值时触发处理机制，建议值：20-40%'
        },
        blackScreen: {
          threshold: 20,
          percentage: 80,
          description: '检测视频中的黑屏现象，当黑屏像素超过设定阈值时触发处理机制'
        },
        enableProcessing: true,
        processingMethod: 'auto_transcode',
        processingPriority: 'medium',
        processingDescription: '自动检测并处理花屏/黑屏问题，可选择自动转码或仅标记异常'
      },
      customRulesData: {
        enableCustomRules: false,
        uploadedRules: [],
        ruleDescription: '上传自定义的视频处理规则文件，支持Python文件格式，文件大小不超过1MB',
        processingCode: '',
        codeDescription: '自定义视频处理逻辑，使用Python语法编写处理函数'
      }
    }
  },
  methods: {
    handleStaticDetectionChange(data) {
      console.log('静态检测参数变更:', data)
    },
    
    handleScreenDetectionChange(data) {
      console.log('花屏/黑屏检测参数变更:', data)
    },
    
    handleCustomRulesChange(data) {
      console.log('自定义规则参数变更:', data)
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-section {
  margin-bottom: 40px;
}

.test-section h2 {
  margin-bottom: 20px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.test-section h3 {
  margin: 20px 0 10px 0;
  color: #606266;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
