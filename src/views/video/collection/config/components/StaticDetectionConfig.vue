<template>
  <div class="config-section">
    <div class="section-header">
      <h3 class="section-title">
        <i class="el-icon-setting"></i>
        静态检测参数与处理
      </h3>
    </div>
    
    <div class="section-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px">
        <!-- 检测阈值设置 -->
        <div class="config-group">
          <h4 class="group-title">检测阈值设置</h4>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="检测阈值" prop="detectionThreshold">
                <div class="slider-container">
                  <el-slider
                    v-model="formData.detectionThreshold"
                    :min="0"
                    :max="100"
                    :step="1"
                    show-input
                    show-stops
                    :marks="{ 50: '50%', 95: '95%' }"
                    @change="handleThresholdChange"
                  />
                  <div class="slider-description">
                    <span>当检测到的静态内容超过此阈值时，触发处理机制。建议值：3-10%</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="转码阈值" prop="transcodeThreshold">
                <div class="slider-container">
                  <el-slider
                    v-model="formData.transcodeThreshold"
                    :min="0"
                    :max="100"
                    :step="1"
                    show-input
                    show-stops
                    :marks="{ 50: '50%', 95: '95%' }"
                    @change="handleTranscodeThresholdChange"
                  />
                  <div class="slider-description">
                    <span>当静态内容超过此阈值时，自动进行转码处理。建议值：90-95%</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 转码参数设置 -->
        <div class="config-group">
          <h4 class="group-title">转码参数设置</h4>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <el-switch
                  v-model="formData.enableTranscode"
                  active-text="启用转码处理"
                  inactive-text="禁用转码处理"
                  @change="handleTranscodeToggle"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <template v-if="formData.enableTranscode">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="最大文件大小" prop="transcodeParams.maxSize">
                  <el-input-number
                    v-model="formData.transcodeParams.maxSize"
                    :min="1"
                    :max="100"
                    :step="0.1"
                    :precision="1"
                    controls-position="right"
                  />
                  <span class="input-suffix">GB</span>
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item label="转码格式" prop="transcodeParams.transcodeFormat">
                  <el-select v-model="formData.transcodeParams.transcodeFormat" placeholder="请选择转码格式">
                    <el-option label="H.264" value="H264" />
                    <el-option label="H.265" value="H265" />
                    <el-option label="MPEG-4" value="MPEG4" />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item>
                  <el-switch
                    v-model="formData.transcodeParams.enableAutoTranscode"
                    active-text="启用自动转码"
                    inactive-text="手动转码"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item label="转码说明">
                  <div class="description-box">
                    <p>{{ formData.transcodeParams.description }}</p>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StaticDetectionConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        detectionThreshold: 50,
        transcodeThreshold: 95,
        enableTranscode: true,
        transcodeParams: {
          maxSize: 3,
          enableAutoTranscode: true,
          transcodeFormat: 'H264',
          description: '自动转码处理，可设置最大文件大小限制，超出限制时自动进行转码处理'
        }
      },
      rules: {
        detectionThreshold: [
          { required: true, message: '请设置检测阈值', trigger: 'change' },
          { type: 'number', min: 0, max: 100, message: '阈值范围为0-100', trigger: 'change' }
        ],
        transcodeThreshold: [
          { required: true, message: '请设置转码阈值', trigger: 'change' },
          { type: 'number', min: 0, max: 100, message: '阈值范围为0-100', trigger: 'change' }
        ],
        'transcodeParams.maxSize': [
          { required: true, message: '请设置最大文件大小', trigger: 'change' },
          { type: 'number', min: 1, max: 100, message: '文件大小范围为1-100GB', trigger: 'change' }
        ],
        'transcodeParams.transcodeFormat': [
          { required: true, message: '请选择转码格式', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...this.formData, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 检测阈值变更
    handleThresholdChange(value) {
      console.log('检测阈值变更:', value)
    },

    // 转码阈值变更
    handleTranscodeThresholdChange(value) {
      console.log('转码阈值变更:', value)
    },

    // 转码开关切换
    handleTranscodeToggle(value) {
      console.log('转码开关切换:', value)
    },

    // 验证表单
    validate() {
      return new Promise((resolve) => {
        this.$refs.formRef.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
    }
  }
}
</script>

<style scoped>
.config-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-content {
  padding: 24px;
}

.config-group {
  margin-bottom: 32px;
}

.config-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.slider-container {
  width: 100%;
}

.slider-description {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.input-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.description-box {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.description-box p {
  margin: 0;
}

/* 滑块样式优化 */
:deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #909399;
}

:deep(.el-slider__stop) {
  background-color: #409eff;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
