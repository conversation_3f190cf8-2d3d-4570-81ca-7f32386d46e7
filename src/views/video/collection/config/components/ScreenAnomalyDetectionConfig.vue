<template>
  <div class="config-section">
    <div class="section-header">
      <h3 class="section-title">
        <i class="el-icon-view"></i>
        花屏/黑屏检测与处理
      </h3>
      <div class="section-description">
        检测并处理视频中的花屏、黑屏等异常现象，支持多种处理策略
      </div>
    </div>

    <div class="section-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="160px">
        <!-- 启用检测开关 -->
        <div class="config-group">
          <el-form-item>
            <el-switch
              v-model="formData.enableDetection"
              active-text="启用异常检测"
              inactive-text="禁用异常检测"
              active-color="#67C23A"
              inactive-color="#DCDFE6"
              @change="handleDetectionToggle"
            />
          </el-form-item>
        </div>

        <template v-if="formData.enableDetection">
          <!-- 黑屏检测设置 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-moon-night"></i>
              黑屏检测设置
            </h4>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="黑屏亮度阈值" prop="blackScreen.brightnessThreshold">
                  <div class="slider-container">
                    <el-slider
                      v-model="formData.blackScreen.brightnessThreshold"
                      :min="0"
                      :max="100"
                      :step="1"
                      show-input
                      show-stops
                      :marks="{ 15: '15', 30: '30', 50: '50' }"
                      @change="handleBlackScreenBrightnessChange"
                    />
                    <div class="slider-description">
                      <i class="el-icon-info"></i>
                      <span>图像亮度低于此值判断为黑屏。建议值：10-30</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="黑屏像素比例" prop="blackScreen.pixelRatioThreshold">
                  <div class="slider-container">
                    <el-slider
                      v-model="formData.blackScreen.pixelRatioThreshold"
                      :min="50"
                      :max="100"
                      :step="1"
                      show-input
                      show-stops
                      :marks="{ 70: '70%', 85: '85%', 95: '95%' }"
                      @change="handleBlackScreenRatioChange"
                    />
                    <div class="slider-description">
                      <i class="el-icon-info"></i>
                      <span>黑屏像素比例超过此值触发处理。建议值：80-95%</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 花屏检测设置 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-picture-outline"></i>
              花屏检测设置
            </h4>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="异常比例阈值" prop="flowerScreen.anomalyRatioThreshold">
                  <div class="slider-container">
                    <el-slider
                      v-model="formData.flowerScreen.anomalyRatioThreshold"
                      :min="10"
                      :max="50"
                      :step="1"
                      show-input
                      show-stops
                      :marks="{ 15: '15%', 25: '25%', 40: '40%' }"
                      @change="handleFlowerScreenAnomalyChange"
                    />
                    <div class="slider-description">
                      <i class="el-icon-info"></i>
                      <span>图像异常比例超过此值判断为花屏。建议值：20-35%</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="噪点密度阈值" prop="flowerScreen.noiseDensityThreshold">
                  <div class="slider-container">
                    <el-slider
                      v-model="formData.flowerScreen.noiseDensityThreshold"
                      :min="10"
                      :max="60"
                      :step="1"
                      show-input
                      show-stops
                      :marks="{ 20: '20%', 30: '30%', 50: '50%' }"
                      @change="handleFlowerScreenNoiseChange"
                    />
                    <div class="slider-description">
                      <i class="el-icon-info"></i>
                      <span>噪点密度超过此值触发花屏检测。建议值：25-45%</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 异常持续时间设置 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-time"></i>
              异常持续时间设置
            </h4>

            <el-row>
              <el-col :span="12">
                <el-form-item label="最小异常持续时间" prop="minAnomalyDuration">
                  <div class="duration-container">
                    <el-input-number
                      v-model="formData.minAnomalyDuration"
                      :min="0.5"
                      :max="10"
                      :step="0.5"
                      :precision="1"
                      controls-position="right"
                      @change="handleDurationChange"
                    />
                    <span class="input-suffix">秒</span>
                    <div class="duration-description">
                      <i class="el-icon-info"></i>
                      <span>异常需持续超过此时间才触发处理机制</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 处理方式设置 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-s-tools"></i>
              处理方式设置
            </h4>

            <el-row>
              <el-col :span="24">
                <el-form-item label="处理策略" prop="processingMethod">
                  <el-radio-group v-model="formData.processingMethod" @change="handleProcessingMethodChange">
                    <el-radio-button label="auto_delete">
                      <i class="el-icon-delete"></i>
                      自动删除异常帧
                    </el-radio-button>
                    <el-radio-button label="replace_normal">
                      <i class="el-icon-refresh"></i>
                      使用正常帧替换
                    </el-radio-button>
                    <el-radio-button label="log_only">
                      <i class="el-icon-warning"></i>
                      仅记录并报警
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 处理方式说明 -->
            <el-row>
              <el-col :span="24">
                <div class="processing-description">
                  <div class="description-item" v-if="formData.processingMethod === 'auto_delete'">
                    <i class="el-icon-delete description-icon danger"></i>
                    <div class="description-content">
                      <h5>自动删除异常帧</h5>
                      <p>检测到异常帧时自动删除，可有效清理视频质量问题，但可能造成视频不连续。</p>
                    </div>
                  </div>
                  <div class="description-item" v-if="formData.processingMethod === 'replace_normal'">
                    <i class="el-icon-refresh description-icon success"></i>
                    <div class="description-content">
                      <h5>使用正常帧替换</h5>
                      <p>用前一帧或后一帧正常画面替换异常帧，保持视频连续性的同时修复质量问题。</p>
                    </div>
                  </div>
                  <div class="description-item" v-if="formData.processingMethod === 'log_only'">
                    <i class="el-icon-warning description-icon warning"></i>
                    <div class="description-content">
                      <h5>仅记录并报警</h5>
                      <p>检测到异常时仅记录日志并发出报警，不修改视频内容，适用于需要人工审核的场景。</p>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScreenAnomalyDetectionConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        // 黑屏检测配置
        blackScreen: {
          // 黑屏判定阈值：通过图像亮度值判断是否为黑屏
          brightnessThreshold: 15, // 亮度阈值 0-100
          // 黑屏像素比例阈值
          pixelRatioThreshold: 85, // 像素比例 50-100%
          description: '检测视频中的黑屏现象，当图像亮度低于阈值且黑屏像素比例超过设定值时触发处理'
        },
        // 花屏检测配置
        flowerScreen: {
          // 花屏判定阈值：通过图像异常比例判定
          anomalyRatioThreshold: 25, // 异常比例 10-50%
          // 噪点密度阈值
          noiseDensityThreshold: 30, // 噪点密度 10-60%
          description: '检测视频中的花屏现象，当图像异常比例和噪点密度超过设定阈值时触发处理'
        },
        // 最小异常持续时间：定义异常需持续多久才触发处理
        minAnomalyDuration: 2, // 持续时间（秒） 0.5-10
        // 处理方式
        processingMethod: 'auto_delete', // auto_delete: 自动删除异常帧, replace_normal: 使用正常帧替换, log_only: 仅记录并报警不修改视频
        // 启用异常检测
        enableDetection: true,
        // 描述信息
        description: '检测并处理视频中的花屏、黑屏等异常现象，支持多种处理策略'
      },
      rules: {
        'blackScreen.brightnessThreshold': [
          { required: true, message: '请设置黑屏亮度阈值', trigger: 'change' },
          { type: 'number', min: 0, max: 100, message: '亮度阈值范围为0-100', trigger: 'change' }
        ],
        'blackScreen.pixelRatioThreshold': [
          { required: true, message: '请设置黑屏像素比例阈值', trigger: 'change' },
          { type: 'number', min: 50, max: 100, message: '像素比例范围为50-100%', trigger: 'change' }
        ],
        'flowerScreen.anomalyRatioThreshold': [
          { required: true, message: '请设置花屏异常比例阈值', trigger: 'change' },
          { type: 'number', min: 10, max: 50, message: '异常比例范围为10-50%', trigger: 'change' }
        ],
        'flowerScreen.noiseDensityThreshold': [
          { required: true, message: '请设置噪点密度阈值', trigger: 'change' },
          { type: 'number', min: 10, max: 60, message: '噪点密度范围为10-60%', trigger: 'change' }
        ],
        minAnomalyDuration: [
          { required: true, message: '请设置最小异常持续时间', trigger: 'change' },
          { type: 'number', min: 0.5, max: 10, message: '持续时间范围为0.5-10秒', trigger: 'change' }
        ],
        processingMethod: [
          { required: true, message: '请选择处理方式', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...this.formData, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 检测开关切换
    handleDetectionToggle(value) {
      console.log('异常检测开关切换:', value)
      if (!value) {
        // 禁用检测时重置处理方式
        this.formData.processingMethod = 'log_only'
      }
    },

    // 黑屏亮度阈值变更
    handleBlackScreenBrightnessChange(value) {
      console.log('黑屏亮度阈值变更:', value)
    },

    // 黑屏像素比例变更
    handleBlackScreenRatioChange(value) {
      console.log('黑屏像素比例变更:', value)
    },

    // 花屏异常比例变更
    handleFlowerScreenAnomalyChange(value) {
      console.log('花屏异常比例变更:', value)
    },

    // 花屏噪点密度变更
    handleFlowerScreenNoiseChange(value) {
      console.log('花屏噪点密度变更:', value)
    },

    // 异常持续时间变更
    handleDurationChange(value) {
      console.log('异常持续时间变更:', value)
    },

    // 处理方式变更
    handleProcessingMethodChange(value) {
      console.log('处理方式变更:', value)
    },

    // 验证表单
    validate() {
      return new Promise((resolve) => {
        if (!this.formData.enableDetection) {
          resolve(true)
          return
        }

        this.$refs.formRef.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
    },

    // 获取配置摘要
    getConfigSummary() {
      if (!this.formData.enableDetection) {
        return '异常检测已禁用'
      }

      const methodMap = {
        'auto_delete': '自动删除',
        'replace_normal': '正常帧替换',
        'log_only': '仅记录报警'
      }

      return `黑屏阈值: ${this.formData.blackScreen.brightnessThreshold}, 花屏阈值: ${this.formData.flowerScreen.anomalyRatioThreshold}%, 持续时间: ${this.formData.minAnomalyDuration}s, 处理方式: ${methodMap[this.formData.processingMethod]}`
    }
  }
}
</script>

<style scoped>
.config-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.section-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 28px;
  color: white;
  position: relative;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.section-title i {
  font-size: 20px;
  color: #ffd700;
}

.section-description {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 1;
}

.section-content {
  padding: 28px;
}

.config-group {
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.config-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  padding-bottom: 12px;
  border-bottom: 2px solid #667eea;
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-title i {
  font-size: 18px;
}

.slider-container {
  width: 100%;
}

.slider-description {
  margin-top: 12px;
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.slider-description i {
  color: #667eea;
  margin-top: 2px;
  flex-shrink: 0;
}

.duration-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-direction: column;
  align-items: flex-start;
}

.duration-container .el-input-number {
  width: 200px;
}

.input-suffix {
  margin-left: 8px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.duration-description {
  margin-top: 8px;
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 6px;
  border-left: 3px solid #667eea;
  width: 100%;
}

.duration-description i {
  color: #667eea;
  margin-top: 2px;
  flex-shrink: 0;
}

.processing-description {
  margin-top: 16px;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border-left: 4px solid;
}

.description-icon {
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.description-icon.danger {
  color: #f56565;
}

.description-icon.warning {
  color: #ed8936;
}

.description-icon.success {
  color: #48bb78;
}

.description-content h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
}

.description-content p {
  margin: 0;
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
}

/* Element UI 组件样式优化 */
:deep(.el-switch__label) {
  font-weight: 500;
  color: #4a5568;
}

:deep(.el-switch__label.is-active) {
  color: #667eea;
}

:deep(.el-radio-button__inner) {
  border-radius: 8px;
  border-color: #667eea;
  color: #667eea;
  font-weight: 500;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

:deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

:deep(.el-slider__runway) {
  background-color: #e2e8f0;
  border-radius: 6px;
  height: 8px;
}

:deep(.el-slider__bar) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
}

:deep(.el-slider__button) {
  border: 3px solid #667eea;
  background: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

:deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #2d3748;
}

:deep(.el-input-number) {
  border-radius: 8px;
}

:deep(.el-input-number .el-input__inner) {
  border-radius: 8px;
  border-color: #e2e8f0;
}

:deep(.el-input-number .el-input__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}
</style>
