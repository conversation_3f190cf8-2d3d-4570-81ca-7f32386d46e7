<template>
  <div class="config-section">
    <div class="section-header">
      <h3 class="section-title">
        <i class="el-icon-video-pause"></i>
        静态帧检测与处理
      </h3>
      <div class="section-description">
        检测视频中的静态帧（如卡顿、静止画面），支持多种处理策略
      </div>
    </div>

    <div class="section-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="160px">
        <!-- 启用检测开关 -->
        <div class="config-group">
          <el-form-item>
            <el-switch
              v-model="formData.enableDetection"
              active-text="启用静态帧检测"
              inactive-text="禁用静态帧检测"
              active-color="#67C23A"
              inactive-color="#DCDFE6"
              @change="handleDetectionToggle"
            />
          </el-form-item>
        </div>

        <template v-if="formData.enableDetection">
          <!-- 检测参数设置 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-data-analysis"></i>
              检测参数设置
            </h4>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="连续帧数阈值" prop="consecutiveFrameThreshold">
                  <div class="slider-container">
                    <el-slider
                      v-model="formData.consecutiveFrameThreshold"
                      :min="1"
                      :max="30"
                      :step="1"
                      show-input
                      show-stops
                      :marks="{ 5: '5帧', 10: '10帧', 20: '20帧' }"
                      @change="handleConsecutiveFrameChange"
                    />
                    <div class="slider-description">
                      <i class="el-icon-info"></i>
                      <span>连续相似帧数超过此值时判断为静态帧。建议值：3-10帧</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="特征相似度阈值" prop="similarityThreshold">
                  <div class="slider-container">
                    <el-slider
                      v-model="formData.similarityThreshold"
                      :min="80"
                      :max="99"
                      :step="1"
                      show-input
                      show-stops
                      :marks="{ 85: '85%', 95: '95%', 98: '98%' }"
                      @change="handleSimilarityThresholdChange"
                    />
                    <div class="slider-description">
                      <i class="el-icon-info"></i>
                      <span>两帧间相似度超过此值判断为静态。建议值：90-98%</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 处理方式设置 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-s-tools"></i>
              处理方式设置
            </h4>

            <el-row>
              <el-col :span="24">
                <el-form-item label="处理策略" prop="processingMethod">
                  <el-radio-group v-model="formData.processingMethod" @change="handleProcessingMethodChange">
                    <el-radio-button label="auto_delete">
                      <i class="el-icon-delete"></i>
                      自动删除静态帧
                    </el-radio-button>
                    <el-radio-button label="mark_only">
                      <i class="el-icon-warning"></i>
                      仅标记，不删除
                    </el-radio-button>
                    <el-radio-button label="compress">
                      <i class="el-icon-compress"></i>
                      压缩处理（保留首帧）
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 处理方式说明 -->
            <el-row>
              <el-col :span="24">
                <div class="processing-description">
                  <div class="description-item" v-if="formData.processingMethod === 'auto_delete'">
                    <i class="el-icon-delete description-icon danger"></i>
                    <div class="description-content">
                      <h5>自动删除静态帧</h5>
                      <p>检测到静态帧时自动删除，可有效减少视频文件大小，但可能影响视频连续性。</p>
                    </div>
                  </div>
                  <div class="description-item" v-if="formData.processingMethod === 'mark_only'">
                    <i class="el-icon-warning description-icon warning"></i>
                    <div class="description-content">
                      <h5>仅标记，不删除</h5>
                      <p>检测到静态帧时仅进行标记记录，不对视频进行修改，适用于需要保持原始视频完整性的场景。</p>
                    </div>
                  </div>
                  <div class="description-item" v-if="formData.processingMethod === 'compress'">
                    <i class="el-icon-compress description-icon success"></i>
                    <div class="description-content">
                      <h5>压缩处理（保留首帧）</h5>
                      <p>检测到连续静态帧时保留第一帧，删除后续重复帧，在保持视频连续性的同时减少文件大小。</p>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StaticFrameDetectionConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        // 连续帧数阈值：用于判断画面是否为静止帧
        consecutiveFrameThreshold: 5, // 连续帧数 1-30
        // 特征相似度阈值：两个画面帧之间的相似度超过该值则判断为静止帧
        similarityThreshold: 95, // 相似度百分比 80-99%
        // 处理方式
        processingMethod: 'auto_delete', // auto_delete: 自动删除, mark_only: 仅标记不删除, compress: 压缩处理(保留首帧)
        // 启用静态帧检测
        enableDetection: true,
        // 描述信息
        description: '检测视频中的静态帧（如卡顿、静止画面），当连续帧数和相似度超过设定阈值时触发处理机制'
      },
      rules: {
        consecutiveFrameThreshold: [
          { required: true, message: '请设置连续帧数阈值', trigger: 'change' },
          { type: 'number', min: 1, max: 30, message: '连续帧数范围为1-30', trigger: 'change' }
        ],
        similarityThreshold: [
          { required: true, message: '请设置特征相似度阈值', trigger: 'change' },
          { type: 'number', min: 80, max: 99, message: '相似度范围为80-99%', trigger: 'change' }
        ],
        processingMethod: [
          { required: true, message: '请选择处理方式', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...this.formData, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 检测开关切换
    handleDetectionToggle(value) {
      console.log('静态帧检测开关切换:', value)
      if (!value) {
        // 禁用检测时重置处理方式
        this.formData.processingMethod = 'mark_only'
      }
    },

    // 连续帧数阈值变更
    handleConsecutiveFrameChange(value) {
      console.log('连续帧数阈值变更:', value)
      // 可以在这里添加实时预览逻辑
    },

    // 相似度阈值变更
    handleSimilarityThresholdChange(value) {
      console.log('特征相似度阈值变更:', value)
      // 可以在这里添加实时预览逻辑
    },

    // 处理方式变更
    handleProcessingMethodChange(value) {
      console.log('处理方式变更:', value)
      // 可以在这里添加处理方式说明的动态更新
    },

    // 验证表单
    validate() {
      return new Promise((resolve) => {
        if (!this.formData.enableDetection) {
          resolve(true)
          return
        }

        this.$refs.formRef.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
    },

    // 获取配置摘要
    getConfigSummary() {
      if (!this.formData.enableDetection) {
        return '静态帧检测已禁用'
      }

      const methodMap = {
        'auto_delete': '自动删除',
        'mark_only': '仅标记',
        'compress': '压缩处理'
      }

      return `连续帧数: ${this.formData.consecutiveFrameThreshold}帧, 相似度: ${this.formData.similarityThreshold}%, 处理方式: ${methodMap[this.formData.processingMethod]}`
    }
  }
}
</script>

<style scoped>
.config-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.section-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 28px;
  color: white;
  position: relative;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.section-title i {
  font-size: 20px;
  color: #ffd700;
}

.section-description {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 1;
}

.section-content {
  padding: 28px;
}

.config-group {
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.config-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  padding-bottom: 12px;
  border-bottom: 2px solid #667eea;
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-title i {
  font-size: 18px;
}

.slider-container {
  width: 100%;
}

.slider-description {
  margin-top: 12px;
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.slider-description i {
  color: #667eea;
  margin-top: 2px;
  flex-shrink: 0;
}

.processing-description {
  margin-top: 16px;
}

.description-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border-left: 4px solid;
}

.description-icon {
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.description-icon.danger {
  color: #f56565;
}

.description-icon.warning {
  color: #ed8936;
}

.description-icon.success {
  color: #48bb78;
}

.description-content h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
}

.description-content p {
  margin: 0;
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
}

/* Element UI 组件样式优化 */
:deep(.el-switch__label) {
  font-weight: 500;
  color: #4a5568;
}

:deep(.el-switch__label.is-active) {
  color: #667eea;
}

:deep(.el-radio-button__inner) {
  border-radius: 8px;
  border-color: #667eea;
  color: #667eea;
  font-weight: 500;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

:deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

:deep(.el-slider__runway) {
  background-color: #e2e8f0;
  border-radius: 6px;
  height: 8px;
}

:deep(.el-slider__bar) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
}

:deep(.el-slider__button) {
  border: 3px solid #667eea;
  background: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

:deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #2d3748;
}
</style>
