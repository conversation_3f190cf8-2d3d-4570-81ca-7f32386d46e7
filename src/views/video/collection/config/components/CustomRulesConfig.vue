<template>
  <div class="config-section">
    <div class="section-header">
      <h3 class="section-title">
        <i class="el-icon-document"></i>
        自定义规则设置
      </h3>
    </div>

    <div class="section-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px">
        <!-- 自定义规则开关 -->
        <div class="config-group">
          <el-row>
            <el-col :span="24">
              <el-form-item>
                <el-switch
                  v-model="formData.enableCustomRules"
                  active-text="启用自定义规则"
                  inactive-text="使用默认规则"
                  @change="handleCustomRulesToggle"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <template v-if="formData.enableCustomRules">
          <!-- 上传自定义规则文件 -->
          <div class="config-group">
            <h4 class="group-title">上传自定义规则文件</h4>

            <el-row>
              <el-col :span="24">
                <el-form-item label="规则文件上传">
                  <div class="upload-container">
                    <FileUpload
                      ref="fileUploadRef"
                      :file-size="1"
                      :file-type="['py']"
                      :limit="1"
                      @success="handleFileUploadSuccess"
                      @error="handleFileUploadError"
                      @remove="handleFileRemove"
                    />
                    <div class="upload-description">
                      <p>{{ formData.ruleDescription }}</p>
                      <p class="upload-tips">
                        <i class="el-icon-info"></i>
                        支持.py格式文件，文件大小不超过1MB
                      </p>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 已上传文件列表 -->
            <el-row v-if="formData.uploadedRules.length > 0">
              <el-col :span="24">
                <el-form-item label="已上传文件">
                  <div class="uploaded-files">
                    <div
                      v-for="(file, index) in formData.uploadedRules"
                      :key="index"
                      class="file-item"
                    >
                      <div class="file-info">
                        <i class="el-icon-document"></i>
                        <span class="file-name">{{ file.name }}</span>
                        <span class="file-size">{{ formatFileSize(file.size) }}</span>
                      </div>
                      <div class="file-actions">
                        <el-button type="text" size="mini" @click="handleFilePreview(file)">
                          预览
                        </el-button>
                        <el-button type="text" size="mini" @click="handleFileDownload(file)">
                          下载
                        </el-button>
                        <el-button type="text" size="mini" class="danger" @click="handleFileDelete(index)">
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 自定义处理代码 -->
          <div class="config-group">
            <h4 class="group-title">自定义处理代码</h4>

            <el-row>
              <el-col :span="24">
                <el-form-item label="处理函数代码">
                  <div class="code-editor-container">
                    <el-input
                      v-model="formData.processingCode"
                      type="textarea"
                      :rows="15"
                      placeholder="请输入自定义处理代码..."
                      class="code-editor"
                    />
                    <div class="code-description">
                      <p>{{ formData.codeDescription }}</p>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 代码模板示例 -->
            <el-row>
              <el-col :span="24">
                <el-form-item label="代码模板">
                  <div class="code-template">
                    <el-collapse>
                      <el-collapse-item title="查看代码模板示例" name="template">
                        <div class="template-content">
                          <pre><code>{{ codeTemplate }}</code></pre>
                        </div>
                        <div class="template-actions">
                          <el-button size="mini" @click="useTemplate">使用此模板</el-button>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </template>
      </el-form>
    </div>

    <!-- 文件预览对话框 -->
    <el-dialog
      title="文件预览"
      :visible.sync="previewDialogVisible"
      width="60%"
      append-to-body
    >
      <div class="file-preview">
        <pre><code>{{ previewContent }}</code></pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CustomRulesConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        enableCustomRules: false,
        uploadedRules: [],
        ruleDescription: '上传自定义的视频处理规则文件，支持Python文件格式，文件大小不超过1MB',
        processingCode: '',
        codeDescription: '自定义视频处理逻辑，使用Python语法编写处理函数'
      },
      rules: {
        processingCode: [
          { required: false, message: '请输入处理代码', trigger: 'blur' }
        ]
      },
      previewDialogVisible: false,
      previewContent: '',
      codeTemplate: `def process_frame(frame, metadata):
    """
    自定义视频帧处理函数

    参数:
        frame: 视频帧数据，numpy数组格式
        metadata: 视频元数据字典，包含帧率、分辨率等信息

    返回:
        processed_frames: 处理后的视频帧数据
        report: 处理报告字典
    """

    # 示例：检测静态帧
    import cv2
    import numpy as np

    # 转换为灰度图
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # 计算帧差
    if hasattr(process_frame, 'prev_frame'):
        diff = cv2.absdiff(gray, process_frame.prev_frame)
        motion_score = np.mean(diff)
    else:
        motion_score = 0

    process_frame.prev_frame = gray

    # 生成处理报告
    report = {
        'motion_score': motion_score,
        'is_static': motion_score < 5,
        'frame_quality': 'good' if motion_score > 10 else 'poor'
    }

    return frame, report`
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...this.formData, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 自定义规则开关切换
    handleCustomRulesToggle(value) {
      console.log('自定义规则开关切换:', value)
    },

    // 文件上传成功
    handleFileUploadSuccess(response, file) {
      const fileInfo = {
        name: file.name,
        size: file.size,
        url: response.url,
        uploadTime: new Date().toISOString()
      }
      this.formData.uploadedRules.push(fileInfo)
      this.$message.success('文件上传成功')
    },

    // 文件上传失败
    handleFileUploadError(error) {
      console.error('文件上传失败:', error)
      this.$message.error('文件上传失败')
    },

    // 文件移除
    handleFileRemove(file) {
      const index = this.formData.uploadedRules.findIndex(item => item.name === file.name)
      if (index > -1) {
        this.formData.uploadedRules.splice(index, 1)
      }
    },

    // 文件预览
    handleFilePreview(file) {
      // 这里应该从服务器获取文件内容
      this.previewContent = `# 文件预览: ${file.name}\n# 实际使用时需要从服务器获取文件内容\n\n# 示例内容:\ndef custom_process():\n    pass`
      this.previewDialogVisible = true
    },

    // 文件下载
    handleFileDownload(file) {
      window.open(file.url, '_blank')
    },

    // 删除文件
    handleFileDelete(index) {
      this.$confirm('确定要删除此文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formData.uploadedRules.splice(index, 1)
        this.$message.success('删除成功')
      }).catch(() => {
        // 取消删除
      })
    },

    // 使用代码模板
    useTemplate() {
      this.formData.processingCode = this.codeTemplate
      this.$message.success('已应用代码模板')
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + ' KB'
      } else {
        return (size / (1024 * 1024)).toFixed(1) + ' MB'
      }
    },

    // 验证表单
    validate() {
      return new Promise((resolve) => {
        this.$refs.formRef.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
      this.formData.uploadedRules = []
      this.formData.processingCode = ''
    }
  }
}
</script>

<style scoped>
.config-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-content {
  padding: 24px;
}

.config-group {
  margin-bottom: 32px;
}

.config-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #e6a23c;
  padding-bottom: 8px;
  border-bottom: 2px solid #e6a23c;
}

.upload-container {
  width: 100%;
}

.upload-description {
  margin-top: 12px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.upload-tips {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.uploaded-files {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.file-actions .danger {
  color: #f56c6c;
}

.code-editor-container {
  width: 100%;
}

.code-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.code-description {
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.code-template {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.template-content {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
}

.template-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #303133;
}

.template-actions {
  margin-top: 12px;
  text-align: right;
}

.file-preview {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}

.file-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
