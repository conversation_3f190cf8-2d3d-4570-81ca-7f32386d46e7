<template>
  <div class="config-section">
    <div class="section-header">
      <h3 class="section-title">
        <i class="el-icon-view"></i>
        花屏/黑屏检测与处理
      </h3>
    </div>
    
    <div class="section-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px">
        <!-- 花屏检测设置 -->
        <div class="config-group">
          <h4 class="group-title">花屏检测设置</h4>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="花屏检测阈值" prop="flowerScreen.threshold">
                <div class="slider-container">
                  <el-slider
                    v-model="formData.flowerScreen.threshold"
                    :min="1"
                    :max="100"
                    :step="1"
                    show-input
                    show-stops
                    :marks="{ 5: '5', 20: '20', 50: '50' }"
                    @change="handleFlowerScreenThresholdChange"
                  />
                  <div class="slider-description">
                    <span>检测花屏现象的敏感度，数值越小越敏感。建议值：5-20</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="花屏检测百分比" prop="flowerScreen.percentage">
                <div class="slider-container">
                  <el-slider
                    v-model="formData.flowerScreen.percentage"
                    :min="10"
                    :max="50"
                    :step="1"
                    show-input
                    show-stops
                    :marks="{ 10: '10%', 30: '30%', 50: '50%' }"
                    @change="handleFlowerScreenPercentageChange"
                  />
                  <div class="slider-description">
                    <span>{{ formData.flowerScreen.description }}</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 黑屏检测设置 -->
        <div class="config-group">
          <h4 class="group-title">黑屏检测设置</h4>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="黑屏检测阈值" prop="blackScreen.threshold">
                <div class="slider-container">
                  <el-slider
                    v-model="formData.blackScreen.threshold"
                    :min="1"
                    :max="100"
                    :step="1"
                    show-input
                    show-stops
                    :marks="{ 20: '20', 50: '50', 80: '80' }"
                    @change="handleBlackScreenThresholdChange"
                  />
                  <div class="slider-description">
                    <span>检测黑屏现象的敏感度，数值越小越敏感。建议值：20-40</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="黑屏检测百分比" prop="blackScreen.percentage">
                <div class="slider-container">
                  <el-slider
                    v-model="formData.blackScreen.percentage"
                    :min="10"
                    :max="100"
                    :step="1"
                    show-input
                    show-stops
                    :marks="{ 50: '50%', 80: '80%', 100: '100%' }"
                    @change="handleBlackScreenPercentageChange"
                  />
                  <div class="slider-description">
                    <span>{{ formData.blackScreen.description }}</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 处理方式设置 -->
        <div class="config-group">
          <h4 class="group-title">处理方式设置</h4>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <el-switch
                  v-model="formData.enableProcessing"
                  active-text="启用检测处理"
                  inactive-text="仅检测不处理"
                  @change="handleProcessingToggle"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <template v-if="formData.enableProcessing">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="处理方式" prop="processingMethod">
                  <el-select v-model="formData.processingMethod" placeholder="请选择处理方式">
                    <el-option label="自动转码" value="auto_transcode" />
                    <el-option label="标记异常" value="mark_exception" />
                    <el-option label="不处理" value="no_process" />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="处理优先级">
                  <el-radio-group v-model="formData.processingPriority">
                    <el-radio label="high">高</el-radio>
                    <el-radio label="medium">中</el-radio>
                    <el-radio label="low">低</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item label="处理说明">
                  <div class="description-box">
                    <p>{{ formData.processingDescription }}</p>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScreenDetectionConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        flowerScreen: {
          threshold: 5,
          percentage: 30,
          description: '检测视频中的花屏现象，当花屏像素超过设定阈值时触发处理机制，建议值：20-40%'
        },
        blackScreen: {
          threshold: 20,
          percentage: 80,
          description: '检测视频中的黑屏现象，当黑屏像素超过设定阈值时触发处理机制'
        },
        enableProcessing: true,
        processingMethod: 'auto_transcode',
        processingPriority: 'medium',
        processingDescription: '自动检测并处理花屏/黑屏问题，可选择自动转码或仅标记异常'
      },
      rules: {
        'flowerScreen.threshold': [
          { required: true, message: '请设置花屏检测阈值', trigger: 'change' },
          { type: 'number', min: 1, max: 100, message: '阈值范围为1-100', trigger: 'change' }
        ],
        'flowerScreen.percentage': [
          { required: true, message: '请设置花屏检测百分比', trigger: 'change' },
          { type: 'number', min: 10, max: 50, message: '百分比范围为10%-50%', trigger: 'change' }
        ],
        'blackScreen.threshold': [
          { required: true, message: '请设置黑屏检测阈值', trigger: 'change' },
          { type: 'number', min: 1, max: 100, message: '阈值范围为1-100', trigger: 'change' }
        ],
        'blackScreen.percentage': [
          { required: true, message: '请设置黑屏检测百分比', trigger: 'change' },
          { type: 'number', min: 10, max: 100, message: '百分比范围为10%-100%', trigger: 'change' }
        ],
        processingMethod: [
          { required: true, message: '请选择处理方式', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...this.formData, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 花屏阈值变更
    handleFlowerScreenThresholdChange(value) {
      console.log('花屏检测阈值变更:', value)
    },

    // 花屏百分比变更
    handleFlowerScreenPercentageChange(value) {
      console.log('花屏检测百分比变更:', value)
    },

    // 黑屏阈值变更
    handleBlackScreenThresholdChange(value) {
      console.log('黑屏检测阈值变更:', value)
    },

    // 黑屏百分比变更
    handleBlackScreenPercentageChange(value) {
      console.log('黑屏检测百分比变更:', value)
    },

    // 处理开关切换
    handleProcessingToggle(value) {
      console.log('处理开关切换:', value)
    },

    // 验证表单
    validate() {
      return new Promise((resolve) => {
        this.$refs.formRef.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
    }
  }
}
</script>

<style scoped>
.config-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-content {
  padding: 24px;
}

.config-group {
  margin-bottom: 32px;
}

.config-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #67c23a;
  padding-bottom: 8px;
  border-bottom: 2px solid #67c23a;
}

.slider-container {
  width: 100%;
}

.slider-description {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.description-box {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.description-box p {
  margin: 0;
}

/* 滑块样式优化 */
:deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #909399;
}

:deep(.el-slider__stop) {
  background-color: #67c23a;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
