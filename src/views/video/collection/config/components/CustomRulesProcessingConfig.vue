<template>
  <div class="config-section">
    <div class="section-header">
      <h3 class="section-title">
        <i class="el-icon-document"></i>
        自定义规则处理
      </h3>
      <div class="section-description">
        上传自定义Python脚本扩展检测逻辑，支持高级自定义处理策略
      </div>
    </div>

    <div class="section-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="160px">
        <!-- 启用自定义规则开关 -->
        <div class="config-group">
          <el-form-item>
            <el-switch
              v-model="formData.enableCustomRules"
              active-text="启用自定义规则"
              inactive-text="使用默认规则"
              active-color="#67C23A"
              inactive-color="#DCDFE6"
              @change="handleCustomRulesToggle"
            />
          </el-form-item>
        </div>

        <template v-if="formData.enableCustomRules">
          <!-- 脚本上传区域 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-upload"></i>
              Python脚本上传
            </h4>

            <el-row>
              <el-col :span="24">
                <el-form-item label="脚本文件上传">
                  <div class="upload-container">
                    <FileUpload
                      ref="fileUploadRef"
                      v-model="formData.uploadedScripts"
                      :file-size="1"
                      :file-type="['py']"
                      :limit="1"
                      @success="handleFileUploadSuccess"
                      @error="handleFileUploadError"
                      @remove="handleFileRemove"
                    />
                    <div class="upload-description">
                      <div class="upload-tips">
                        <i class="el-icon-info"></i>
                        <span>支持.py格式文件，文件大小不超过1MB</span>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 脚本功能说明 -->
            <el-row v-if="formData.uploadedScripts.length > 0">
              <el-col :span="24">
                <el-form-item label="脚本功能说明">
                  <el-input
                    v-model="formData.scriptDescription"
                    type="textarea"
                    :rows="3"
                    placeholder="请描述脚本的功能和用途..."
                    @change="handleScriptDescriptionChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 脚本启用状态 -->
            <el-row v-if="formData.uploadedScripts.length > 0">
              <el-col :span="24">
                <el-form-item label="脚本状态">
                  <el-switch
                    v-model="formData.scriptEnabled"
                    active-text="脚本已启用"
                    inactive-text="脚本已禁用"
                    active-color="#67C23A"
                    inactive-color="#F56C6C"
                    @change="handleScriptEnabledChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 接口说明与模板 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-document-copy"></i>
              脚本接口说明
            </h4>

            <el-row>
              <el-col :span="24">
                <div class="interface-description">
                  <div class="description-card">
                    <h5>
                      <i class="el-icon-info"></i>
                      接口规范
                    </h5>
                    <p>{{ formData.interfaceDescription }}</p>
                  </div>
                </div>
              </el-col>
            </el-row>

            <!-- 函数模板展示 -->
            <el-row>
              <el-col :span="24">
                <el-form-item label="处理函数模板">
                  <div class="template-container">
                    <div class="template-header">
                      <span class="template-title">
                        <i class="el-icon-document"></i>
                        process_frame.py
                      </span>
                      <el-button
                        type="text"
                        size="mini"
                        @click="copyTemplate"
                      >
                        <i class="el-icon-document-copy"></i>
                        复制模板
                      </el-button>
                    </div>
                    <div class="code-block">
                      <pre><code>{{ formData.functionTemplate }}</code></pre>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 使用说明 -->
          <div class="config-group">
            <h4 class="group-title">
              <i class="el-icon-question"></i>
              使用说明
            </h4>

            <el-row>
              <el-col :span="24">
                <div class="usage-guide">
                  <div class="guide-item">
                    <div class="guide-step">1</div>
                    <div class="guide-content">
                      <h6>编写处理函数</h6>
                      <p>根据上述模板编写自定义的帧处理函数，实现特定的检测逻辑</p>
                    </div>
                  </div>
                  <div class="guide-item">
                    <div class="guide-step">2</div>
                    <div class="guide-content">
                      <h6>上传Python文件</h6>
                      <p>将编写好的.py文件上传到系统，文件大小不超过1MB</p>
                    </div>
                  </div>
                  <div class="guide-item">
                    <div class="guide-step">3</div>
                    <div class="guide-content">
                      <h6>配置并启用</h6>
                      <p>填写脚本功能说明，启用脚本后即可在视频处理中生效</p>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomRulesProcessingConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        // 启用自定义规则
        enableCustomRules: false,
        // 已上传的Python脚本文件
        uploadedScripts: [],
        // 脚本功能说明
        scriptDescription: '',
        // 脚本启用状态
        scriptEnabled: false,
        // 接口说明和模板
        interfaceDescription: '自定义脚本需要实现标准的处理函数接口，支持帧级别的检测和处理逻辑',
        // 处理函数模板
        functionTemplate: `def process_frame(frame_data, frame_index, metadata):
    """
    自定义帧处理函数

    参数:
        frame_data: 帧图像数据 (numpy array)
        frame_index: 帧索引 (int)
        metadata: 帧元数据 (dict)

    返回:
        dict: {
            'action': 'keep|delete|replace',  # 处理动作
            'confidence': 0.0-1.0,           # 置信度
            'reason': 'string'               # 处理原因
        }
    """
    # 在此处编写自定义检测逻辑
    return {
        'action': 'keep',
        'confidence': 1.0,
        'reason': 'normal_frame'
    }`,
        // 描述信息
        description: '上传自定义Python脚本扩展检测逻辑，支持高级自定义处理策略'
      },
      rules: {
        scriptDescription: [
          { required: true, message: '请填写脚本功能说明', trigger: 'blur' },
          { min: 10, max: 500, message: '功能说明长度在10到500个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...this.formData, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 自定义规则开关切换
    handleCustomRulesToggle(value) {
      console.log('自定义规则开关切换:', value)
      if (!value) {
        // 禁用自定义规则时重置相关状态
        this.formData.scriptEnabled = false
        this.formData.scriptDescription = ''
      }
    },

    // 文件上传成功
    handleFileUploadSuccess(response, file) {
      console.log('文件上传成功:', response, file)
      this.$message.success('脚本文件上传成功')
    },

    // 文件上传失败
    handleFileUploadError(error) {
      console.error('文件上传失败:', error)
      this.$message.error('脚本文件上传失败，请重试')
    },

    // 文件移除
    handleFileRemove(file) {
      console.log('文件移除:', file)
      this.formData.scriptEnabled = false
      this.formData.scriptDescription = ''
      this.$message.info('脚本文件已移除')
    },

    // 脚本功能说明变更
    handleScriptDescriptionChange(value) {
      console.log('脚本功能说明变更:', value)
    },

    // 脚本启用状态变更
    handleScriptEnabledChange(value) {
      console.log('脚本启用状态变更:', value)
      if (value && !this.formData.scriptDescription.trim()) {
        this.$message.warning('请先填写脚本功能说明')
        this.formData.scriptEnabled = false
        return
      }
    },

    // 复制模板代码
    copyTemplate() {
      const textarea = document.createElement('textarea')
      textarea.value = this.formData.functionTemplate
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('模板代码已复制到剪贴板')
    },

    // 验证表单
    validate() {
      return new Promise((resolve) => {
        if (!this.formData.enableCustomRules) {
          resolve(true)
          return
        }

        if (this.formData.uploadedScripts.length === 0) {
          resolve(true)
          return
        }

        this.$refs.formRef.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
      this.formData.uploadedScripts = []
      this.formData.scriptEnabled = false
    },

    // 获取配置摘要
    getConfigSummary() {
      if (!this.formData.enableCustomRules) {
        return '自定义规则已禁用'
      }

      if (this.formData.uploadedScripts.length === 0) {
        return '未上传自定义脚本'
      }

      const status = this.formData.scriptEnabled ? '已启用' : '已禁用'
      return `自定义脚本: ${this.formData.uploadedScripts.length}个文件, 状态: ${status}`
    }
  }
}
</script>

<style scoped>
.config-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.section-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 28px;
  color: white;
  position: relative;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.section-title i {
  font-size: 20px;
  color: #ffd700;
}

.section-description {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 1;
}

.section-content {
  padding: 28px;
}

.config-group {
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.config-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  padding-bottom: 12px;
  border-bottom: 2px solid #667eea;
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-title i {
  font-size: 18px;
}

.upload-container {
  width: 100%;
}

.upload-description {
  margin-top: 12px;
}

.upload-tips {
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.upload-tips i {
  color: #667eea;
  flex-shrink: 0;
}

.interface-description {
  margin-bottom: 16px;
}

.description-card {
  padding: 16px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.description-card h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
  display: flex;
  align-items: center;
  gap: 6px;
}

.description-card p {
  margin: 0;
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
}

.template-container {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: #1a202c;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #2d3748;
  border-bottom: 1px solid #4a5568;
}

.template-title {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.template-title i {
  color: #ffd700;
}

.code-block {
  padding: 16px;
  background: #1a202c;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #e2e8f0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.code-block code {
  color: #e2e8f0;
}

.usage-guide {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.guide-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.guide-step {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.guide-content h6 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
}

.guide-content p {
  margin: 0;
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
}

/* Element UI 组件样式优化 */
:deep(.el-switch__label) {
  font-weight: 500;
  color: #4a5568;
}

:deep(.el-switch__label.is-active) {
  color: #667eea;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  border-color: #e2e8f0;
  font-family: inherit;
}

:deep(.el-textarea__inner:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #2d3748;
}

:deep(.el-button--text) {
  color: #667eea;
  font-weight: 500;
}

:deep(.el-button--text:hover) {
  color: #764ba2;
}
</style>
